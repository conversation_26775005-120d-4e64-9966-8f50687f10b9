# Live2D 模型变换工具

这个工具库提供了一套完整的函数来计算和应用 Live2D 模型的最佳缩放和位置。

## 主要功能

### 1. 自动计算模型变换

根据模型的边界信息和 Canvas 尺寸，自动计算最佳的缩放比例和位置。

```javascript
import { calculateModelTransform, applyModelTransform } from './utils/live2dUtils';

// 获取模型边界和 Canvas 尺寸
const modelBounds = live2dModel.getBounds();
const canvasSize = {
  width: pixiApp.screen.width,
  height: pixiApp.screen.height
};

// 计算变换
const transform = calculateModelTransform(modelBounds, canvasSize, {
  padding: 0.1,     // 10% 边距
  maxScale: 1.5,    // 最大缩放 1.5 倍
  minScale: 0.1,    // 最小缩放 0.1 倍
  fitMode: 'contain', // 适配模式
  anchor: { x: 0.5, y: 0.5 } // 锚点位置（居中）
});

// 应用变换到模型
applyModelTransform(live2dModel, transform);
```

### 2. 预设配置

提供了多种预设配置，适用于不同的使用场景：

```javascript
import { PRESET_CONFIGS } from './utils/live2dUtils';

// 使用预设配置
const config = PRESET_CONFIGS.default;     // 默认配置
const config = PRESET_CONFIGS.fullscreen;  // 全屏配置
const config = PRESET_CONFIGS.compact;     // 紧凑配置
const config = PRESET_CONFIGS.bottomAlign; // 底部对齐
```

### 3. 响应式变换

自动响应窗口大小变化，重新计算模型位置：

```javascript
import { createResponsiveModelTransform } from './utils/live2dUtils';

// 创建响应式更新函数
const responsiveUpdate = createResponsiveModelTransform(live2dModel, pixiApp, config);

// 监听窗口大小变化
const handleResize = () => {
  responsiveUpdate();
};
window.addEventListener('resize', handleResize);
```

## 配置选项详解

### padding (边距)
- 类型: `number` (0-1)
- 默认值: `0.1`
- 说明: 模型周围的边距比例，0.1 表示 10% 的边距

### maxScale / minScale (缩放限制)
- 类型: `number`
- 默认值: `maxScale: 2.0`, `minScale: 0.1`
- 说明: 限制模型的最大和最小缩放比例

### fitMode (适配模式)
- 类型: `string`
- 可选值: `'contain'`, `'cover'`, `'fill'`
- 默认值: `'contain'`
- 说明:
  - `contain`: 完全显示模型，可能有空白区域
  - `cover`: 填满容器，可能裁剪模型
  - `fill`: 填充模式，可能导致变形

### anchor (锚点位置)
- 类型: `object` `{x: number, y: number}`
- 默认值: `{x: 0.5, y: 0.5}`
- 说明: 模型在 Canvas 中的锚点位置
  - `{x: 0.5, y: 0.5}`: 居中
  - `{x: 0.5, y: 1.0}`: 底部居中
  - `{x: 0.0, y: 0.5}`: 左侧居中

## 预设配置详情

### default (默认)
```javascript
{
  padding: 0.1,
  maxScale: 1.5,
  minScale: 0.1,
  fitMode: 'contain',
  anchor: { x: 0.5, y: 0.5 }
}
```
适用于大多数场景，模型居中显示，有适当的边距。

### fullscreen (全屏)
```javascript
{
  padding: 0,
  maxScale: 3.0,
  minScale: 0.5,
  fitMode: 'cover',
  anchor: { x: 0.5, y: 0.5 }
}
```
模型填满整个屏幕，无边距，适合全屏展示。

### compact (紧凑)
```javascript
{
  padding: 0.05,
  maxScale: 1.0,
  minScale: 0.2,
  fitMode: 'contain',
  anchor: { x: 0.5, y: 0.5 }
}
```
较小的模型尺寸，适合在侧边栏或小窗口中显示。

### bottomAlign (底部对齐)
```javascript
{
  padding: 0.1,
  maxScale: 1.5,
  minScale: 0.1,
  fitMode: 'contain',
  anchor: { x: 0.5, y: 0.8 }
}
```
模型底部对齐到 Canvas 底部，适合聊天界面等场景。

## 调试信息

`calculateModelTransform` 函数返回的对象包含详细的调试信息：

```javascript
const transform = calculateModelTransform(modelBounds, canvasSize, config);

console.log('变换结果:', {
  scale: transform.scale,
  position: transform.position
});

console.log('调试信息:', transform.debug);
```

调试信息包含：
- 原始模型边界
- Canvas 尺寸
- 可用显示区域
- 缩放后的尺寸
- 各种缩放因子

## 完整使用示例

```javascript
import * as PIXI from 'pixi.js';
import { Live2DModel } from 'pixi-live2d-display';
import { 
  calculateModelTransform, 
  applyModelTransform, 
  PRESET_CONFIGS,
  createResponsiveModelTransform 
} from './utils/live2dUtils';

async function initLive2D() {
  // 创建 PIXI 应用
  const app = new PIXI.Application({
    width: 1024,
    height: 1024,
    backgroundColor: 0xffffff
  });

  // 加载模型
  const model = await Live2DModel.from('/path/to/model.model3.json');
  app.stage.addChild(model);

  // 计算和应用变换
  const modelBounds = model.getBounds();
  const canvasSize = { width: app.screen.width, height: app.screen.height };
  const transform = calculateModelTransform(modelBounds, canvasSize, PRESET_CONFIGS.default);
  
  applyModelTransform(model, transform);

  // 设置响应式更新
  const responsiveUpdate = createResponsiveModelTransform(model, app, PRESET_CONFIGS.default);
  window.addEventListener('resize', responsiveUpdate);

  return { app, model };
}
```

这套工具函数让您可以轻松地在任何尺寸的 Canvas 中完美显示 Live2D 模型，无需手动调整复杂的数学计算。
