/**
 * Live2D 模型工具函数
 */

/**
 * 计算 Live2D 模型的最佳缩放和位置
 * @param {Object} modelBounds - 模型边界信息 (来自 model.getBounds())
 * @param {Object} canvasSize - Canvas 尺寸信息
 * @param {number} canvasSize.width - Canvas 宽度
 * @param {number} canvasSize.height - Canvas 高度
 * @param {Object} options - 可选配置
 * @param {number} options.padding - 边距比例 (0-1)，默认 0.1 (10% 边距)
 * @param {number} options.maxScale - 最大缩放比例，默认 2.0
 * @param {number} options.minScale - 最小缩放比例，默认 0.1
 * @param {string} options.fitMode - 适配模式: 'contain'(包含), 'cover'(覆盖), 'fill'(填充)
 * @param {Object} options.anchor - 锚点位置 {x: 0-1, y: 0-1}，默认居中 {x: 0.5, y: 0.5}
 * @returns {Object} 包含 scale 和 position 的对象
 */
export function calculateModelTransform(modelBounds, canvasSize, options = {}) {
  const {
    padding = 0.1,
    maxScale = 2.0,
    minScale = 0.1,
    fitMode = 'contain',
    anchor = { x: 0.5, y: 0.5 }
  } = options;

  // 获取模型的实际尺寸
  const modelWidth = modelBounds.width;
  const modelHeight = modelBounds.height;
  
  // 计算可用的 Canvas 尺寸（减去边距）
  const availableWidth = canvasSize.width * (1 - padding * 2);
  const availableHeight = canvasSize.height * (1 - padding * 2);
  
  // 根据适配模式计算缩放比例
  const scaleX = availableWidth / modelWidth;
  const scaleY = availableHeight / modelHeight;

  let scale;
  switch (fitMode) {
    case 'cover':
      // 覆盖模式：选择较大的比例，确保填满容器
      scale = Math.max(scaleX, scaleY);
      break;
    case 'fill':
      // 填充模式：可能会变形，分别缩放 X 和 Y
      // 这里返回平均值，实际使用时可能需要分别设置
      scale = (scaleX + scaleY) / 2;
      break;
    case 'contain':
    default:
      // 包含模式：选择较小的比例，确保模型完全适合
      scale = Math.min(scaleX, scaleY);
      break;
  }

  // 限制缩放范围
  scale = Math.max(minScale, Math.min(maxScale, scale));
  
  // 计算缩放后的模型尺寸
  const scaledWidth = modelWidth * scale;
  const scaledHeight = modelHeight * scale;
  
  // 根据锚点计算目标位置
  const targetX = canvasSize.width * anchor.x;
  const targetY = canvasSize.height * anchor.y;

  // 计算位置，考虑模型边界的偏移
  // PIXI 的锚点默认在左上角，所以需要考虑模型的边界偏移
  const positionX = targetX - (modelBounds.x + modelWidth / 2) * scale;
  const positionY = targetY - (modelBounds.y + modelHeight / 2) * scale;
  
  return {
    scale: scale,
    position: {
      x: positionX,
      y: positionY
    },
    // 额外信息，用于调试
    debug: {
      modelBounds: {
        x: modelBounds.x,
        y: modelBounds.y,
        width: modelWidth,
        height: modelHeight
      },
      canvasSize: {
        width: canvasSize.width,
        height: canvasSize.height
      },
      availableSize: {
        width: availableWidth,
        height: availableHeight
      },
      scaledSize: {
        width: scaledWidth,
        height: scaledHeight
      },
      scaleFactors: {
        scaleX: scaleX,
        scaleY: scaleY,
        finalScale: scale
      }
    }
  };
}

/**
 * 应用变换到 Live2D 模型
 * @param {Object} model - Live2D 模型实例
 * @param {Object} transform - 变换信息 (来自 calculateModelTransform)
 */
export function applyModelTransform(model, transform) {
  if (!model || !transform) {
    console.warn('模型或变换信息无效');
    return;
  }
  
  // 设置缩放
  model.scale.set(transform.scale);
  
  // 设置位置
  model.position.set(transform.position.x, transform.position.y);
  
  console.log('已应用模型变换:', {
    scale: transform.scale,
    position: transform.position
  });
}

/**
 * 获取模型在 Canvas 中的实际显示区域
 * @param {Object} model - Live2D 模型实例
 * @returns {Object} 显示区域信息
 */
export function getModelDisplayBounds(model) {
  if (!model) {
    return null;
  }

  const bounds = model.getBounds();

  return {
    x: bounds.x,
    y: bounds.y,
    width: bounds.width,
    height: bounds.height,
    centerX: bounds.x + bounds.width / 2,
    centerY: bounds.y + bounds.height / 2
  };
}

/**
 * 创建响应式的模型变换函数
 * @param {Object} model - Live2D 模型实例
 * @param {Object} app - PIXI 应用实例
 * @param {Object} options - 配置选项
 * @returns {Function} 更新函数
 */
export function createResponsiveModelTransform(model, app, options = {}) {
  let lastCanvasSize = { width: 0, height: 0 };

  const updateTransform = () => {
    const currentSize = {
      width: app.screen.width,
      height: app.screen.height
    };

    // 检查尺寸是否发生变化
    if (currentSize.width !== lastCanvasSize.width ||
        currentSize.height !== lastCanvasSize.height) {

      const modelBounds = model.getBounds();
      const transform = calculateModelTransform(modelBounds, currentSize, options);
      applyModelTransform(model, transform);

      lastCanvasSize = { ...currentSize };

      console.log('响应式更新模型变换:', {
        canvasSize: currentSize,
        transform: {
          scale: transform.scale,
          position: transform.position
        }
      });
    }
  };

  return updateTransform;
}

/**
 * 预设的模型适配配置
 */
export const PRESET_CONFIGS = {
  // 默认配置：居中显示，10% 边距
  default: {
    padding: 0.1,
    maxScale: 1.5,
    minScale: 0.1,
    fitMode: 'contain',
    anchor: { x: 0.5, y: 0.5 }
  },

  // 全屏配置：填满整个屏幕
  fullscreen: {
    padding: 0,
    maxScale: 3.0,
    minScale: 0.5,
    fitMode: 'cover',
    anchor: { x: 0.5, y: 0.5 }
  },

  // 紧凑配置：较小的模型，适合侧边栏
  compact: {
    padding: 0.05,
    maxScale: 1.0,
    minScale: 0.2,
    fitMode: 'contain',
    anchor: { x: 0.5, y: 0.5 }
  },

  // 底部对齐：模型底部对齐到 Canvas 底部
  bottomAlign: {
    padding: 0.1,
    maxScale: 1.5,
    minScale: 0.1,
    fitMode: 'contain',
    anchor: { x: 0.5, y: 0.8 }
  }
};
