import { useState } from 'react';
import { PRESET_CONFIGS, calculateModelTransform, applyModelTransform } from '../utils/live2dUtils';

/**
 * 模型配置面板组件
 * 用于实时调整 Live2D 模型的显示参数
 */
export function ModelConfigPanel({ model, app, onConfigChange }) {
  const [selectedPreset, setSelectedPreset] = useState('default');
  const [customConfig, setCustomConfig] = useState(PRESET_CONFIGS.default);
  const [showDebugInfo, setShowDebugInfo] = useState(false);

  // 应用配置到模型
  const applyConfig = (config) => {
    if (!model || !app) return;

    const modelBounds = model.getBounds();
    const canvasSize = {
      width: app.screen.width,
      height: app.screen.height
    };

    const transform = calculateModelTransform(modelBounds, canvasSize, config);
    applyModelTransform(model, transform);

    if (showDebugInfo) {
      console.log('应用配置:', config);
      console.log('变换结果:', transform);
    }

    if (onConfigChange) {
      onConfigChange(config, transform);
    }
  };

  // 选择预设配置
  const handlePresetChange = (presetName) => {
    setSelectedPreset(presetName);
    const config = PRESET_CONFIGS[presetName];
    setCustomConfig(config);
    applyConfig(config);
  };

  // 更新自定义配置
  const updateCustomConfig = (key, value) => {
    const newConfig = {
      ...customConfig,
      [key]: value
    };
    setCustomConfig(newConfig);
    applyConfig(newConfig);
  };

  // 更新锚点配置
  const updateAnchor = (axis, value) => {
    const newConfig = {
      ...customConfig,
      anchor: {
        ...customConfig.anchor,
        [axis]: value
      }
    };
    setCustomConfig(newConfig);
    applyConfig(newConfig);
  };

  return (
    <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <h3 className="text-lg font-semibold mb-4">模型配置</h3>
      
      {/* 预设配置选择 */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          预设配置
        </label>
        <select
          value={selectedPreset}
          onChange={(e) => handlePresetChange(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="default">默认 (居中，10% 边距)</option>
          <option value="fullscreen">全屏 (填满屏幕)</option>
          <option value="compact">紧凑 (适合侧边栏)</option>
          <option value="bottomAlign">底部对齐</option>
        </select>
      </div>

      {/* 自定义配置 */}
      <div className="space-y-4">
        {/* 边距 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            边距: {(customConfig.padding * 100).toFixed(0)}%
          </label>
          <input
            type="range"
            min="0"
            max="0.3"
            step="0.01"
            value={customConfig.padding}
            onChange={(e) => updateCustomConfig('padding', parseFloat(e.target.value))}
            className="w-full"
          />
        </div>

        {/* 最大缩放 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            最大缩放: {customConfig.maxScale.toFixed(1)}x
          </label>
          <input
            type="range"
            min="0.5"
            max="3"
            step="0.1"
            value={customConfig.maxScale}
            onChange={(e) => updateCustomConfig('maxScale', parseFloat(e.target.value))}
            className="w-full"
          />
        </div>

        {/* 最小缩放 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            最小缩放: {customConfig.minScale.toFixed(1)}x
          </label>
          <input
            type="range"
            min="0.1"
            max="1"
            step="0.1"
            value={customConfig.minScale}
            onChange={(e) => updateCustomConfig('minScale', parseFloat(e.target.value))}
            className="w-full"
          />
        </div>

        {/* 适配模式 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            适配模式
          </label>
          <select
            value={customConfig.fitMode}
            onChange={(e) => updateCustomConfig('fitMode', e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="contain">包含 (完全显示)</option>
            <option value="cover">覆盖 (填满容器)</option>
            <option value="fill">填充 (可能变形)</option>
          </select>
        </div>

        {/* 锚点位置 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            锚点位置
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <label className="block text-xs text-gray-600 mb-1">
                水平: {(customConfig.anchor.x * 100).toFixed(0)}%
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={customConfig.anchor.x}
                onChange={(e) => updateAnchor('x', parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">
                垂直: {(customConfig.anchor.y * 100).toFixed(0)}%
              </label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={customConfig.anchor.y}
                onChange={(e) => updateAnchor('y', parseFloat(e.target.value))}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>

      {/* 调试信息开关 */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <label className="flex items-center">
          <input
            type="checkbox"
            checked={showDebugInfo}
            onChange={(e) => setShowDebugInfo(e.target.checked)}
            className="mr-2"
          />
          <span className="text-sm text-gray-700">显示调试信息</span>
        </label>
      </div>
    </div>
  );
}

export default ModelConfigPanel;
