import { useState } from 'react';
import { CANVAS_PRESETS, getEnvironmentInfo } from '../config/pixiConfig';

/**
 * Canvas 配置面板组件
 * 用于测试不同的 Canvas 尺寸和分辨率设置
 */
export function CanvasConfigPanel({ onConfigChange }) {
  const [canvasWidth, setCanvasWidth] = useState(500);
  const [canvasHeight, setCanvasHeight] = useState(500);
  const [selectedPreset, setSelectedPreset] = useState('standard');
  const [showEnvInfo, setShowEnvInfo] = useState(false);

  const envInfo = getEnvironmentInfo();

  // 预设尺寸选项
  const sizePresets = [
    { name: '正方形 500x500', width: 500, height: 500 },
    { name: '正方形 800x800', width: 800, height: 800 },
    { name: '横屏 800x600', width: 800, height: 600 },
    { name: '竖屏 600x800', width: 600, height: 800 },
    { name: '宽屏 1024x576', width: 1024, height: 576 },
    { name: '小尺寸 300x300', width: 300, height: 300 }
  ];

  const handleSizePresetChange = (preset) => {
    setCanvasWidth(preset.width);
    setCanvasHeight(preset.height);
    applyConfig(preset.width, preset.height, selectedPreset);
  };

  const handlePresetChange = (presetName) => {
    setSelectedPreset(presetName);
    applyConfig(canvasWidth, canvasHeight, presetName);
  };

  const handleCustomSizeChange = () => {
    applyConfig(canvasWidth, canvasHeight, selectedPreset);
  };

  const applyConfig = (width, height, preset) => {
    if (onConfigChange) {
      onConfigChange({
        width,
        height,
        preset,
        description: `${width}x${height} - ${CANVAS_PRESETS[preset].description}`
      });
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg">
      <h2 className="text-xl font-semibold mb-4">Canvas 配置</h2>
      
      {/* 尺寸预设 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">尺寸预设</h3>
        <div className="grid grid-cols-2 gap-2">
          {sizePresets.map((preset, index) => (
            <button
              key={index}
              onClick={() => handleSizePresetChange(preset)}
              className={`p-2 text-sm rounded border transition-colors ${
                canvasWidth === preset.width && canvasHeight === preset.height
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
              }`}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>

      {/* 自定义尺寸 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">自定义尺寸</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              宽度
            </label>
            <input
              type="number"
              value={canvasWidth}
              onChange={(e) => setCanvasWidth(parseInt(e.target.value) || 500)}
              onBlur={handleCustomSizeChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="100"
              max="2000"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              高度
            </label>
            <input
              type="number"
              value={canvasHeight}
              onChange={(e) => setCanvasHeight(parseInt(e.target.value) || 500)}
              onBlur={handleCustomSizeChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="100"
              max="2000"
            />
          </div>
        </div>
      </div>

      {/* 分辨率预设 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium mb-3">分辨率设置</h3>
        <div className="space-y-2">
          {Object.entries(CANVAS_PRESETS).map(([presetName, config]) => (
            <label key={presetName} className="flex items-center">
              <input
                type="radio"
                name="resolutionPreset"
                value={presetName}
                checked={selectedPreset === presetName}
                onChange={(e) => handlePresetChange(e.target.value)}
                className="mr-3"
              />
              <div>
                <span className="font-medium capitalize">{presetName}</span>
                <p className="text-sm text-gray-600">{config.description}</p>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* 当前配置信息 */}
      <div className="mb-4 p-3 bg-gray-50 rounded-md">
        <h4 className="font-medium text-gray-700 mb-2">当前配置</h4>
        <p className="text-sm text-gray-600">
          尺寸: {canvasWidth} × {canvasHeight}
        </p>
        <p className="text-sm text-gray-600">
          分辨率: {CANVAS_PRESETS[selectedPreset].description}
        </p>
      </div>

      {/* 环境信息 */}
      <div>
        <button
          onClick={() => setShowEnvInfo(!showEnvInfo)}
          className="text-sm text-blue-600 hover:text-blue-800 mb-2"
        >
          {showEnvInfo ? '隐藏' : '显示'} 环境信息
        </button>
        
        {showEnvInfo && (
          <div className="p-3 bg-blue-50 rounded-md text-sm">
            <p><strong>设备像素比:</strong> {envInfo.devicePixelRatio}</p>
            <p><strong>屏幕尺寸:</strong> {envInfo.screenWidth} × {envInfo.screenHeight}</p>
            <p><strong>视口尺寸:</strong> {envInfo.viewportWidth} × {envInfo.viewportHeight}</p>
            <p><strong>推荐预设:</strong> {envInfo.recommendedPreset}</p>
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="mt-6 p-3 bg-yellow-50 rounded-md">
        <h4 className="font-medium text-yellow-800 mb-2">💡 使用说明</h4>
        <ul className="text-sm text-yellow-700 space-y-1">
          <li>• <strong>standard</strong>: 推荐用于开发和测试，尺寸一致</li>
          <li>• <strong>highDPI</strong>: 自动适配高分屏，更清晰但可能有尺寸差异</li>
          <li>• <strong>retina</strong>: 固定 2x 分辨率，适合 Retina 屏幕</li>
        </ul>
      </div>
    </div>
  );
}

export default CanvasConfigPanel;
