/**
 * PIXI 应用配置
 */

/**
 * Canvas 配置预设
 */
export const CANVAS_PRESETS = {
  // 标准配置：不使用高分辨率，尺寸就是设置的尺寸
  standard: {
    resolution: 1,
    autoDensity: false,
    description: '标准分辨率，Canvas 尺寸与设置一致'
  },
  
  // 高分辨率配置：自动适配设备分辨率，在高分屏上更清晰
  highDPI: {
    resolution: window.devicePixelRatio || 1,
    autoDensity: true,
    description: '高分辨率，自动适配设备像素比'
  },
  
  // 固定高分辨率：强制使用 2x 分辨率
  retina: {
    resolution: 2,
    autoDensity: true,
    description: '固定 2x 分辨率，适合 Retina 屏幕'
  }
};

/**
 * 创建 PIXI 应用配置
 * @param {HTMLCanvasElement} canvas - Canvas 元素
 * @param {number} width - 宽度
 * @param {number} height - 高度
 * @param {string} preset - 预设名称 ('standard', 'highDPI', 'retina')
 * @param {Object} customOptions - 自定义选项
 * @returns {Object} PIXI 应用配置
 */
export function createPixiConfig(canvas, width, height, preset = 'standard', customOptions = {}) {
  const presetConfig = CANVAS_PRESETS[preset] || CANVAS_PRESETS.standard;
  
  const config = {
    view: canvas,
    width: width,
    height: height,
    backgroundColor: 0xffffff,
    antialias: true,
    resolution: presetConfig.resolution,
    autoDensity: presetConfig.autoDensity,
    ...customOptions
  };
  
  console.log(`使用 PIXI 配置预设: ${preset}`, {
    description: presetConfig.description,
    config: {
      width: config.width,
      height: config.height,
      resolution: config.resolution,
      autoDensity: config.autoDensity
    }
  });
  
  return config;
}

/**
 * 获取推荐的预设
 * @returns {string} 推荐的预设名称
 */
export function getRecommendedPreset() {
  const devicePixelRatio = window.devicePixelRatio || 1;
  
  if (devicePixelRatio > 1.5) {
    return 'highDPI'; // 高分辨率屏幕使用 highDPI
  } else {
    return 'standard'; // 普通屏幕使用标准配置
  }
}

/**
 * 检查当前环境信息
 * @returns {Object} 环境信息
 */
export function getEnvironmentInfo() {
  return {
    devicePixelRatio: window.devicePixelRatio || 1,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    viewportWidth: window.innerWidth,
    viewportHeight: window.innerHeight,
    recommendedPreset: getRecommendedPreset()
  };
}
