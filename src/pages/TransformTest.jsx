import { useState, useRef, useEffect } from 'react'
import * as PIXI from 'pixi.js'
import { Live2DModel } from 'pixi-live2d-display'
import { calculateModelTransform, applyModelTransform, PRESET_CONFIGS } from '../utils/live2dUtils'

// 设置全局 PIXI 对象
window.PIXI = PIXI

function TransformTest() {
  const canvasRef = useRef(null)
  const [app, setApp] = useState(null)
  const [model, setModel] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedPreset, setSelectedPreset] = useState('default')
  const [transformInfo, setTransformInfo] = useState(null)

  useEffect(() => {
    let pixiApp = null
    let live2dModel = null

    const initLive2D = async () => {
      try {
        setLoading(true)

        if (!canvasRef.current) return

        // 创建 PIXI 应用
        pixiApp = new PIXI.Application({
          view: canvasRef.current,
          width: 800,
          height: 600,
          backgroundColor: 0xf0f0f0,
          antialias: true,
          resolution: window.devicePixelRatio || 1,
          autoDensity: true,
        })

        setApp(pixiApp)

        // 加载 Live2D 模型
        live2dModel = await Live2DModel.from('/live2d/Idol/ldol.model3.json')
        pixiApp.stage.addChild(live2dModel)

        // 应用默认变换
        applyTransform(live2dModel, pixiApp, 'default')

        // 添加更新循环
        const updateTicker = () => {
          if (live2dModel) {
            live2dModel.update()
          }
        }
        pixiApp.ticker.add(updateTicker)

        setModel(live2dModel)
        setLoading(false)

      } catch (err) {
        console.error('Live2D 初始化失败:', err)
        setLoading(false)
      }
    }

    const timer = setTimeout(() => {
      initLive2D()
    }, 100)

    return () => {
      clearTimeout(timer)
      if (live2dModel) {
        live2dModel.destroy()
      }
      if (pixiApp) {
        pixiApp.destroy(true, true)
      }
    }
  }, [])

  // 应用变换函数
  const applyTransform = (model, app, presetName) => {
    if (!model || !app) return

    const modelBounds = model.getBounds()
    const canvasSize = {
      width: app.screen.width,
      height: app.screen.height
    }

    const config = PRESET_CONFIGS[presetName]
    const transform = calculateModelTransform(modelBounds, canvasSize, config)
    
    applyModelTransform(model, transform)

    // 保存变换信息用于显示
    setTransformInfo({
      presetName,
      config,
      transform,
      modelBounds: {
        x: modelBounds.x,
        y: modelBounds.y,
        width: modelBounds.width,
        height: modelBounds.height
      },
      canvasSize
    })

    console.log(`应用预设 "${presetName}":`, transform)
  }

  // 切换预设
  const handlePresetChange = (presetName) => {
    setSelectedPreset(presetName)
    if (model && app) {
      applyTransform(model, app, presetName)
    }
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 text-center">Live2D 模型变换测试</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：Canvas 显示区域 */}
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-xl font-semibold mb-4">模型显示</h2>
            <div className="relative">
              <canvas
                ref={canvasRef}
                className="border border-gray-300 rounded-lg w-full"
                style={{ maxWidth: '800px', maxHeight: '600px' }}
              />
              
              {loading && (
                <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 rounded-lg">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">正在加载 Live2D 模型...</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 右侧：控制面板和信息显示 */}
          <div className="space-y-6">
            {/* 预设选择 */}
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <h2 className="text-xl font-semibold mb-4">预设配置</h2>
              <div className="grid grid-cols-2 gap-3">
                {Object.keys(PRESET_CONFIGS).map((presetName) => (
                  <button
                    key={presetName}
                    onClick={() => handlePresetChange(presetName)}
                    className={`p-3 rounded-lg border-2 transition-colors ${
                      selectedPreset === presetName
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {presetName === 'default' && '默认'}
                    {presetName === 'fullscreen' && '全屏'}
                    {presetName === 'compact' && '紧凑'}
                    {presetName === 'bottomAlign' && '底部对齐'}
                  </button>
                ))}
              </div>
            </div>

            {/* 变换信息显示 */}
            {transformInfo && (
              <div className="bg-white p-6 rounded-lg shadow-lg">
                <h2 className="text-xl font-semibold mb-4">变换信息</h2>
                <div className="space-y-4 text-sm">
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">当前预设: {transformInfo.presetName}</h3>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">Canvas 尺寸</h3>
                    <p className="text-gray-600">
                      宽度: {transformInfo.canvasSize.width}px, 
                      高度: {transformInfo.canvasSize.height}px
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">模型原始边界</h3>
                    <p className="text-gray-600">
                      X: {transformInfo.modelBounds.x.toFixed(2)}, 
                      Y: {transformInfo.modelBounds.y.toFixed(2)}
                    </p>
                    <p className="text-gray-600">
                      宽度: {transformInfo.modelBounds.width.toFixed(2)}, 
                      高度: {transformInfo.modelBounds.height.toFixed(2)}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">计算结果</h3>
                    <p className="text-gray-600">
                      缩放比例: {transformInfo.transform.scale.toFixed(3)}
                    </p>
                    <p className="text-gray-600">
                      位置 X: {transformInfo.transform.position.x.toFixed(2)}
                    </p>
                    <p className="text-gray-600">
                      位置 Y: {transformInfo.transform.position.y.toFixed(2)}
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-gray-700 mb-2">配置参数</h3>
                    <p className="text-gray-600">
                      边距: {(transformInfo.config.padding * 100).toFixed(0)}%
                    </p>
                    <p className="text-gray-600">
                      适配模式: {transformInfo.config.fitMode}
                    </p>
                    <p className="text-gray-600">
                      锚点: ({transformInfo.config.anchor.x}, {transformInfo.config.anchor.y})
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TransformTest
